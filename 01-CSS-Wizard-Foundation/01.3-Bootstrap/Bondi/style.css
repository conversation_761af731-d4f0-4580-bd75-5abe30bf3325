:root {
    --dark-color: #19283f;
    --green-color: #33d1cc;
    --red-color: #ff3150;
    --yellow-color: #ffc400;
    --section-color: #eff7fa;
}

body {
    font-family: Roboto, sans-serif;
}

.navbar {
    background-color: var(--dark-color);
}
.navbar .navbar-nav .nav-link {
    color: #fff;
}
.navbar .navbar-nav .nav-link.active,
.navbar .navbar-nav .nav-link:focus,
.navbar .navbar-nav .nav-link:hover{
    color: var(--green-color);
}
.search {
    border-left: 1px solid var(--green-color);
    svg{
        color: var(--green-color);
    }
}
.navbar .navbar-toggler {
    border: 0;
    color: #fff;
    font-size: 25px;
}
.navbar .navbar-toggler:focus {
    box-shadow: none;
}
.main-btn {
    background-color: var(--red-color);
    color: var(--yellow-color);
    padding: .5rem 1rem;
}
.landing {
    background-color: var(--dark-color);
    min-height: calc(100vh - 72px);
}

.main-title:after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translate(-50%, 50%);
    width: 120px;
    height: 2px;
    background-color: var(--green-color);
}

.features .icon-holder{
    height: 200px;
}
.features .icon-holder svg{
    left:50%;
    transform: translate(-50%);
}
.features .icon-holder .number{
    font-size: 12rem;
    color: var(--section-color);
}
.features .icon-holder .icon{
    color: var(--green-color);
}

.our-work{
    background-color: var(--section-color);
}

.our-work ul .active{
    background-color: var(--red-color);
    color: var(--yellow-color);
    cursor: pointer;
}

.our-work ul li {
    padding: .5rem 1rem;
    cursor: pointer;
}
.our-work ul li:not(.active):hover {
    color: var(--red-color);
}

.our-work .box{
    padding: 5px;
    overflow: hidden;
    position: relative;
}
.our-work .box::before{
    content: attr(data-work);
    position: absolute;
    background-color: var(--green-color);
    width: calc(100% - 10px);
    height: calc(100% - 10px);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    color: #fff;
    opacity: 0;
    transition: opacity 0.3s ease;
}