<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="lib/swiper.css" />
    <link rel="stylesheet" href="lib/fontawesome.css" />
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/spinner.css" />
    <script src="lib/swiper.js"></script>
    <script src="js/script.js" defer></script>
    <title>Flixx</title>
  </head>
  <body>
    <header class="main-header">
      <div class="container">
        <div class="logo"><a href="index.html">FLIXX</a></div>
        <nav>
          <ul>
            <li>
              <a class="nav-link" href="index.html">Movies</a>
            </li>
            <li>
              <a class="nav-link" href="shows.html">TV Shows</a>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <!-- Now Playing Section -->
    <section class="now-playing">
      <h2>Now Playing</h2>
      <div class="swiper">
        <div class="swiper-wrapper">
          <!-- <div class="swiper-slide">
            <a href="movie-details.html?id=1">
              <img src="./images/no-image.jpg" alt="Movie Title" />
            </a>
            <h4 class="swiper-rating">
              <i class="fas fa-star text-secondary"></i> 8 / 10
            </h4>
          </div> -->
        </div>
      </div>
    </section>

    <!-- Search Movies -->
    <section class="search">
      <div class="container">
        <div id="alert"></div>
        <form action="search.html" class="search-form">
          <!-- movies and shows radio box -->
          <div class="search-radio">
            <input type="radio" id="movie" name="type" value="movie" checked />
            <label for="movies">Movies</label>
            <input type="radio" id="tv" name="type" value="tv" />
            <label for="shows">TV Shows</label>
          </div>
          <div class="search-flex">
            <input
              type="text"
              name="search-term"
              id="search-term"
              placeholder="Enter search term"
            />
            <button class="btn" type="submit">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </form>
      </div>
    </section>

    <!-- Popular Movies -->
    <section class="container">
      <h2>Popular Movies</h2>
      <div id="popular-movies" class="grid">
        <div class="card">
          <a href="movie-details.html?id=1">
            <img
              src="images/no-image.jpg"
              class="card-img-top"
              alt="Movie Title"
            />
          </a>
          <div class="card-body">
            <h5 class="card-title">Movie Title</h5>
            <p class="card-text">
              <small class="text-muted">Release: XX/XX/XXXX</small>
            </p>
          </div>
        </div>
        <div class="card">
          <a href="movie-details.html?id=1">
            <img
              src="images/no-image.jpg"
              class="card-img-top"
              alt="Movie Title"
            />
          </a>
          <div class="card-body">
            <h5 class="card-title">Movie Title</h5>
            <p class="card-text">
              <small class="text-muted">Release: XX/XX/XXXX</small>
            </p>
          </div>
        </div>
        <div class="card">
          <a href="movie-details.html?id=1">
            <img
              src="images/no-image.jpg"
              class="card-img-top"
              alt="Movie Title"
            />
          </a>
          <div class="card-body">
            <h5 class="card-title">Movie Title</h5>
            <p class="card-text">
              <small class="text-muted">Release: XX/XX/XXXX</small>
            </p>
          </div>
        </div>
        <div class="card">
          <a href="movie-details.html?id=1">
            <img
              src="images/no-image.jpg"
              class="card-img-top"
              alt="Movie Title"
            />
          </a>
          <div class="card-body">
            <h5 class="card-title">Movie Title</h5>
            <p class="card-text">
              <small class="text-muted">Release: XX/XX/XXXX</small>
            </p>
          </div>
        </div>
        <div class="card">
          <a href="movie-details.html?id=1">
            <img
              src="images/no-image.jpg"
              class="card-img-top"
              alt="Movie Title"
            />
          </a>
          <div class="card-body">
            <h5 class="card-title">Movie Title</h5>
            <p class="card-text">
              <small class="text-muted">Release: XX/XX/XXXX</small>
            </p>
          </div>
        </div>
        <div class="card">
          <a href="movie-details.html?id=1">
            <img
              src="images/no-image.jpg"
              class="card-img-top"
              alt="Movie Title"
            />
          </a>
          <div class="card-body">
            <h5 class="card-title">Movie Title</h5>
            <p class="card-text">
              <small class="text-muted">Release: XX/XX/XXXX</small>
            </p>
          </div>
        </div>
        <div class="card">
          <a href="movie-details.html?id=1">
            <img
              src="images/no-image.jpg"
              class="card-img-top"
              alt="Movie Title"
            />
          </a>
          <div class="card-body">
            <h5 class="card-title">Movie Title</h5>
            <p class="card-text">
              <small class="text-muted">Release: XX/XX/XXXX</small>
            </p>
          </div>
        </div>
        <div class="card">
          <a href="movie-details.html?id=1">
            <img
              src="images/no-image.jpg"
              class="card-img-top"
              alt="Movie Title"
            />
          </a>
          <div class="card-body">
            <h5 class="card-title">Movie Title</h5>
            <p class="card-text">
              <small class="text-muted">Release: XX/XX/XXXX</small>
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="main-footer">
      <div class="container">
        <div class="logo"><span>FLIXX</span></div>
        <div class="social-links">
          <a href="https://www.facebook.com" target="_blank"
            ><i class="fab fa-facebook-f"></i
          ></a>
          <a href="https://www.twitter.com" target="_blank"
            ><i class="fab fa-twitter"></i
          ></a>
          <a href="https://www.instagram.com" target="_blank"
            ><i class="fab fa-instagram"></i
          ></a>
        </div>
      </div>
    </footer>

    <div class="spinner"></div>
  </body>
</html>
