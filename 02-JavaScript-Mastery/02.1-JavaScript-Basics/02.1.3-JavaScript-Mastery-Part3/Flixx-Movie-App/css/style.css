*,
*::before,
*::after {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

:root {
  --color-primary: #020d18;
  --color-secondary: #f1c40f;
}

body {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  background: var(--color-primary);
  color: #fff;
  overflow-x: hidden;
}

a {
  color: #fff;
  text-decoration: none;
}

ul {
  list-style: none;
}

.text-primary {
  color: var(--color-secondary);
}

.text-secondary {
  color: var(--color-secondary);
}

.active {
  color: var(--color-secondary);
  font-weight: 700;
}

.back {
  margin-top: 30px;
}

.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  border: 1px solid var(--color-secondary);
  border-radius: 5px;
  color: #fff;
  cursor: pointer;
  background: transparent;
  transition: all 0.3s ease-in-out;
}

.btn:hover {
  background: var(--color-secondary);
  color: #000;
}

.btn:disabled {
  border-color: #ccc;
  cursor: not-allowed;
}

.btn:disabled:hover {
  background: transparent;
  color: #fff;
}

.container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;
}

.main-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.main-header {
  padding: 20px 0;
  background: rgba(0, 0, 0, 0.8);
}

.main-header .logo {
  color: #fff;
  font-size: 25px;
  font-weight: 700;
  text-transform: uppercase;
}

.main-header ul {
  display: flex;
}

.main-header ul li {
  margin-left: 20px;
}

.main-header ul li a {
  font-size: 16px;
}

.main-header ul li a:hover {
  color: var(--color-secondary);
}

/* Grid */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  grid-gap: 20px;
}

/* Card */
.card {
  background: #04376b;
  padding: 5px;
}

.card img {
  width: 100%;
}

.card-body {
  padding: 10px;
  font-size: 20px;
}

.card:hover {
  transform: scale(1.05);
  transition: all 0.5s ease-in-out;
  background: #0a4b8f;
}

/* Footer */
.main-footer {
  background: #020d18;
  padding: 20px 0;
  margin-top: 20px;
}

.main-footer .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.main-footer .container .logo {
  color: #fff;
  font-size: 25px;
  font-weight: 700;
  text-transform: uppercase;
}

.main-footer .container .social-links {
  display: flex;
  font-size: 20px;
}

.main-footer .container .social-links a {
  margin-left: 10px;
  color: #fff;
}

.main-footer .container .social-links a:hover {
  color: var(--color-secondary);
}

/* Section: Now Playing */

section.now-playing {
  padding: 60px;
  background: url(../images/showcase-bg.jpg) no-repeat center center/cover;
}

section h2 {
  margin: 20px 0;
  text-align: center;
  text-transform: uppercase;
}

/* Slider */
.swiper {
  width: 100%;
  height: 50%;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  background: var(--color-primary);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.swiper-rating {
  padding: 10px;
}

/* Movie Details */
.details-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 50px 0 30px;
}

.details-top img {
  width: 400px;
  height: 100%;
  margin-right: 60px;
  object-fit: cover;
}

.details-top p {
  margin: 20px 0;
}

.details-top .btn {
  margin-top: 20px;
}

.details-bottom {
}

.details-bottom li {
  margin: 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #fff;
  border-color: rgba(255, 255, 255, 0.1);
}

/* Search */
.search {
  padding: 60px;
  margin-bottom: 40px;
}

.search .container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.search .container h2 {
  margin-bottom: 20px;
}

.search .container form {
  width: 100%;
  max-width: 600px;
}

.search-radio label {
  margin-right: 15px;
}

.search-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.search .container form input[type='text'] {
  flex: 6;
  width: 100%;
  height: 50px;
  padding: 10px;
  margin-right: 10px;
  border: 1px solid #fff;
  border-radius: 5px;
  background: transparent;
  color: #fff;
}

.search .container form input[type='text']::placeholder {
  color: #fff;
}

.search .container form input[type='text']:focus {
  outline: none;
}

.search .container form button {
  flex: 1;
  display: block;
  width: 100%;
  padding: 10px;
  border-radius: 5px;
  height: 50px;
  cursor: pointer;
  background: var(--color-secondary);
  color: #000;
}

.search .container form button:hover {
  background: transparent;
  color: #fff;
}

.pagination {
  margin-top: 20px;
}

.page-counter {
  margin-top: 10px;
}

/* Alert */
.alert {
  background: red;
  padding: 10px 20px;
  margin-bottom: 20px;
  border-radius: 5px;
}

.alert-success {
  background: green;
}

.alert-error {
  background: red;
}
