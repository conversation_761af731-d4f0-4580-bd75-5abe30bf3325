.spinner {
    --frame: 1s;
}
.spinner,
.spinner::before,
.spinner::after {
    border: 5px solid white;
    border-top-color: transparent;
    border-bottom-color: transparent;
    border-radius: 50%;
}
.spinner {
    margin: auto;
    font-size: 4em;
    position: absolute;
    top: 4px;
    left: 140px;

    width: 0.5em;
    height: 0.5em;
    -webkit-animation: anim-spinner var(--frame) ease infinite;
    animation: anim-spinner var(--frame) ease infinite;
}
.spinner::before,
.spinner::after {
    content: '';
    position: absolute;
}
.spinner::before {
    inset: 1px;
    -webkit-animation: anim-spinner calc(var(--frame) * 4.3) ease-in infinite;
    animation: anim-spinner calc(var(--frame) * 4.3) ease-in infinite;
}
.spinner::after {
    inset: 7px;
    -webkit-animation: anim-spinner calc(var(--frame) * 1.51) ease-out infinite;
    animation: anim-spinner calc(var(--frame) * 1.51) ease-out infinite;
}

@-webkit-keyframes anim-spinner {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(359deg);
    }
}
@keyframes anim-spinner {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(359deg);
    }
}
